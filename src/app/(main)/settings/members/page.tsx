'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import styled from 'styled-components';
import SettingsLayout from '../components/SettingsLayout';
import CreateMemberModal from '../components/CreateMemberModal';
import RestoreMemberModal from './components/RestoreMemberModal';
import {
  Plus,
  MoreHorizontal,
  Filter,
  ChevronDown,
  Edit,
  Trash,
  X,
  Check,
  AlertTriangle,
  RotateCcw,
  MessageCircle,
  Search,
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import useUserStore from '@/store/userStore';
import { toast } from 'react-hot-toast';
import { appTheme as settingsTheme, appStyles as commonStyles } from '@/app/theme';
import { navigateToPrivateChat } from '@/services/privateChatHelper';

const MembersContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  box-shadow: ${settingsTheme.shadows.md};

  /* Large desktop */
  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    max-width: 1400px;
    padding: ${settingsTheme.spacing['2xl']};
    gap: ${settingsTheme.spacing['2xl']};
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    max-width: 100%;
    padding: ${settingsTheme.spacing.lg};
    gap: ${settingsTheme.spacing.lg};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.md};
    gap: ${settingsTheme.spacing.md};
    border-radius: ${settingsTheme.borderRadius.sm};
    box-shadow: ${settingsTheme.shadows.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.sm};
    gap: ${settingsTheme.spacing.sm};
    border-radius: 0;
    box-shadow: none;
    background-color: transparent;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
  flex-wrap: wrap;
  gap: ${settingsTheme.spacing.md};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    flex-direction: column;
    align-items: stretch;
    gap: ${settingsTheme.spacing.sm};
    margin-bottom: ${settingsTheme.spacing.md};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    margin-bottom: ${settingsTheme.spacing.sm};
  }
`;

const Title = styled.h2`
  ${commonStyles.sectionTitle}
`;

const AddButton = styled.button`
  ${commonStyles.button.primary}
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    width: 100%;
    justify-content: center;
    min-height: 44px;
    padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.lg};
    font-size: ${settingsTheme.typography.fontSizes.base};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    min-height: 40px;
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
    font-size: ${settingsTheme.typography.fontSizes.sm};
  }
`;

const TableWrapper = styled.div`
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  border-radius: ${settingsTheme.borderRadius.md};
  box-shadow: ${settingsTheme.shadows.sm};

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    border-radius: ${settingsTheme.borderRadius.sm};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    border-radius: ${settingsTheme.borderRadius.xs};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    border-radius: 0;
    box-shadow: none;
    border: 1px solid ${settingsTheme.colors.border};
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  min-width: 600px; /* Ensure table doesn't get too cramped */

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes.sm};
    min-width: 500px;
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    min-width: 400px;
  }
`;

const THead = styled.thead`
  background-color: ${settingsTheme.colors.background.light};
`;

const TH = styled.th`
  text-align: left;
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
  border-bottom: 1px solid ${settingsTheme.colors.border};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.xs};
    font-size: ${settingsTheme.typography.fontSizes.xs};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.xs};
    font-size: 10px;

    &:nth-child(n+4) {
      display: none; /* Hide less important columns on very small screens */
    }
  }
`;

const TR = styled.tr`
  transition: ${settingsTheme.transitions.default};
  &:hover {
    background-color: ${settingsTheme.colors.background.light};
  }
`;

const TD = styled.td`
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  border-bottom: 1px solid ${settingsTheme.colors.border};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.xs};
    font-size: ${settingsTheme.typography.fontSizes.xs};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.xs};
    font-size: 10px;

    &:nth-child(n+4) {
      display: none; /* Hide less important columns on very small screens */
    }
  }
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${settingsTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    background-color: ${settingsTheme.colors.background.lighter};
    color: ${settingsTheme.colors.primary};
  }

  &.edit {
    color: ${settingsTheme.colors.secondary};
  }

  &.delete {
    color: ${settingsTheme.colors.error.main};
  }
`;

// Action menu removed as we're showing buttons directly in the table

// Action menu item removed as we're showing buttons directly in the table

// Modal components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing['2xl']};
  width: 100%;
  max-width: 500px;
  box-shadow: ${settingsTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.xl};
`;

const ModalTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
`;

const ModalCloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${settingsTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    color: ${settingsTheme.colors.text.secondary};
    background-color: ${settingsTheme.colors.background.lighter};
  }
`;

const ModalBody = styled.div`
  margin-bottom: ${settingsTheme.spacing.xl};
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${settingsTheme.spacing.md};
`;

const CancelButton = styled.button`
  ${commonStyles.button.secondary}
`;

const SaveButton = styled.button`
  ${commonStyles.button.primary}
`;

// Delete confirmation modal components
const DeleteModalTitle = styled.h3`
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #f44336;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const WarningText = styled.p`
  margin: 0 0 1rem;
  color: #333;
  line-height: 1.5;
`;

const MemberPreview = styled.div`
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  border-left: 3px solid #f44336;
`;

const DeleteButton = styled.button`
  background-color: ${settingsTheme.colors.error.main};
  color: white;
  border: none;
  border-radius: ${settingsTheme.borderRadius.md};
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: ${settingsTheme.transitions.default};

  &:hover {
    background-color: #d32f2f;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 1rem;
`;

const FormLabel = styled.label`
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
`;

const FormInput = styled.input`
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  color: #111827;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const FormSelect = styled.select`
  width: 100%;
  padding: 0.625rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
`;

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const Avatar = styled.div<{ $imageUrl?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: #4b5563;
  font-size: 0.875rem;
  background-size: cover;
  background-position: center;
  ${props =>
    props.$imageUrl &&
    `
    background-image: url(${props.$imageUrl});
    color: transparent;
  `}
`;

const UserCell = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const UserName = styled.div`
  font-weight: 500;
  color: #111827;
`;

const UserEmail = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #e0f2fe;
  color: #0369a1;
`;

// Filter components
const FiltersContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
`;

const FilterSelect = styled.div`
  position: relative;
  min-width: 200px;
`;

const FilterButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.625rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.875rem;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
`;

// Using transient props with $ prefix to avoid passing them to the DOM
const FilterDropdown = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  z-index: 10;
  display: ${props => (props.$isOpen ? 'block' : 'none')};
  margin-top: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const FilterOption = styled.div<{ $isSelected?: boolean }>`
  padding: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  background-color: ${props => (props.$isSelected ? '#f3f4f6' : 'white')};

  &:hover {
    background-color: #f9fafb;
  }
`;

const FilterLabel = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4b5563;
`;

const SearchInputContainer = styled.div`
  position: relative;
  min-width: 250px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 0.625rem 0.625rem 0.625rem 2.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.875rem;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

const SkeletonContainer = styled.div`
  width: 100%;
  padding: 1rem 0;
`;

const SkeletonTable = styled.div`
  width: 100%;
  border-collapse: collapse;
  box-shadow: ${settingsTheme.shadows.sm};
  border-radius: ${settingsTheme.borderRadius.md};
  overflow: hidden;
  border: 1px solid ${settingsTheme.colors.border};
`;

const SkeletonTableHeader = styled.div`
  display: flex;
  background-color: ${settingsTheme.colors.background.light};
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const SkeletonTableHeaderCell = styled.div`
  height: 16px;
  background-color: ${settingsTheme.colors.background.lighter};
  border-radius: ${settingsTheme.borderRadius.sm};
  animation: pulse 1.5s ease-in-out infinite;
  margin-right: 16px;

  &:nth-child(1) {
    width: 25%;
  }
  &:nth-child(2) {
    width: 15%;
  }
  &:nth-child(3) {
    width: 20%;
  }
  &:nth-child(4) {
    width: 20%;
  }
  &:nth-child(5) {
    width: 10%;
  }
`;

const SkeletonTableRow = styled.div`
  display: flex;
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const SkeletonUserCell = styled.div`
  display: flex;
  align-items: center;
  width: 25%;
  margin-right: 16px;
`;

const SkeletonAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${settingsTheme.colors.background.lighter};
  margin-right: 12px;
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonUserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const SkeletonUserName = styled.div`
  height: 14px;
  width: 120px;
  background-color: ${settingsTheme.colors.background.lighter};
  border-radius: ${settingsTheme.borderRadius.sm};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonUserEmail = styled.div`
  height: 12px;
  width: 150px;
  background-color: ${settingsTheme.colors.background.lighter};
  border-radius: ${settingsTheme.borderRadius.sm};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonBadge = styled.div`
  height: 20px;
  width: 60px;
  background-color: ${settingsTheme.colors.background.lighter};
  border-radius: 9999px;
  animation: pulse 1.5s ease-in-out infinite;
  margin-right: 16px;
`;

const SkeletonText = styled.div`
  height: 14px;
  background-color: ${settingsTheme.colors.background.lighter};
  border-radius: ${settingsTheme.borderRadius.sm};
  animation: pulse 1.5s ease-in-out infinite;
  margin-right: 16px;

  &.department {
    width: 20%;
  }

  &.organization {
    width: 20%;
  }
`;

const SkeletonActions = styled.div`
  display: flex;
  gap: 8px;
  width: 10%;
`;

const SkeletonActionButton = styled.div`
  width: 24px;
  height: 24px;
  border-radius: ${settingsTheme.borderRadius.sm};
  background-color: ${settingsTheme.colors.background.lighter};
  animation: pulse 1.5s ease-in-out infinite;
`;

const GlobalStyle = styled.div`
  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      opacity: 0.6;
    }
  }
`;

const NoResults = styled.div`
  padding: 1rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
`;

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
}

interface Member {
  id: number;
  isLeader: boolean;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    userRoleId: number;
    deletedAt?: string;
  };
  department: {
    id: number;
    name: string;
    organization: {
      id: number;
      name: string;
    };
  };
}

export default function MembersPage() {
  const [members, setMembers] = useState<Member[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [selectedDept, setSelectedDept] = useState<Department | null>(null);
  const [orgDropdownOpen, setOrgDropdownOpen] = useState(false);
  const [deptDropdownOpen, setDeptDropdownOpen] = useState(false);
  const [searchName, setSearchName] = useState('');
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  // Action menu state removed as we're showing buttons directly in the table

  // Edit member state
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<Member | null>(null);
  const [selectedNewOrganization, setSelectedNewOrganization] = useState<number | null>(null);
  const [selectedNewDepartment, setSelectedNewDepartment] = useState<number | null>(null);
  const [availableDepartments, setAvailableDepartments] = useState<Department[]>([]);
  const [editingIsLeader, setEditingIsLeader] = useState<boolean>(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateError, setUpdateError] = useState<string | null>(null);

  // Delete confirmation state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Restore member state
  const [isRestoreModalOpen, setIsRestoreModalOpen] = useState(false);
  const [memberToRestore, setMemberToRestore] = useState<Member | null>(null);

  // Add member state
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getToken } = useAuth();
  const { userData } = useUserStore();
  const router = useRouter();

  // Check if user has admin or owner privileges
  const hasAdminPrivileges = userData?.role?.isAdmin || userData?.role?.isOwner;

  // Check if user is a department leader (member role with leadership in at least one department)
  const isDepartmentLeader = userData?.role?.isMember &&
    userData?.departments?.some(dept => dept.isLeader === true);

  // Get departments where current user is a leader (for department leader filter)
  const leaderDepartments = userData?.departments?.filter(dept => dept.isLeader === true) || [];

  // Action menu ref removed as we're showing buttons directly in the table
  // Fetch organizations on component mount (only for admin/owner users)
  useEffect(() => {
    // Only fetch organizations for admin/owner users
    if (!hasAdminPrivileges) {
      return;
    }

    const fetchOrganizations = async () => {
      try {
        const token = await getToken();
        if (!token) {
          setError('Authentication required');
          return;
        }

        const response = await fetch('/api/v1/organization', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch organizations');
        }

        const data = await response.json();
        const orgs = data.organizations || [];
        setOrganizations(orgs);

        // Set the first organization as default if available
        if (orgs.length > 0) {
          setSelectedOrg(orgs[0]);
        }
      } catch (err) {
        setError('Error fetching organizations');
        console.error(err);
      }
    };

    fetchOrganizations();
  }, [getToken, hasAdminPrivileges]);

  // Fetch departments when organization is selected
  useEffect(() => {
    if (!selectedOrg) {
      setDepartments([]);
      setSelectedDept(null);
      return;
    }

    const fetchDepartments = async () => {
      try {
        const token = await getToken();
        if (!token) {
          setError('Authentication required');
          return;
        }

        const response = await fetch(`/api/v1/department?organizationId=${selectedOrg.id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch departments');
        }

        const data = await response.json();
        setDepartments(data.departments || []);
      } catch (err) {
        setError('Error fetching departments');
        console.error(err);
      }
    };

    fetchDepartments();
  }, [selectedOrg]);

  // Fetch members based on filters
  useEffect(() => {
    fetchMembersData();
  }, [selectedOrg, selectedDept]);

  // Use all members without search filtering
  const filteredMembers = members;

  // Handle organization selection
  const handleOrgSelect = (org: Organization) => {
    // If selecting a different organization, reset department filter
    if (selectedOrg?.id !== org.id) {
      setSelectedDept(null);
    }

    setSelectedOrg(org);
    setOrgDropdownOpen(false);

    // Toast notification for better user feedback
    toast.success(`Organization changed to ${org.name}`);
  };

  // Handle department selection
  const handleDeptSelect = (dept: Department) => {
    setSelectedDept(dept);
    setDeptDropdownOpen(false);
  };

  // Clear filters
  const clearFilters = () => {
    setSelectedOrg(null);
    setSelectedDept(null);
    setSearchName('');
  };

  // Handle search input change with debouncing
  const handleSearchChange = (value: string) => {
    setSearchName(value);

    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set new timeout for debounced search
    const newTimeout = setTimeout(() => {
      // Trigger search after 500ms of no typing
      if (selectedOrg || selectedDept) {
        fetchMembersData();
      }
    }, 500);

    setSearchTimeout(newTimeout);
  };

  // Action menu toggle and click outside handler removed as we're showing buttons directly in the table

  // Handle edit member
  const handleEditMember = async (memberId: number) => {
    // Find the member to edit
    const memberToEdit = members.find(m => m.id === memberId);
    if (memberToEdit) {
      setEditingMember(memberToEdit);
      setSelectedNewOrganization(memberToEdit.department.organization.id);
      setSelectedNewDepartment(memberToEdit.department.id);
      setEditingIsLeader(memberToEdit.isLeader);
      setAvailableDepartments(
        departments.filter(dept => departments.some(d => d.id === memberToEdit.department.id))
      );
      setIsEditModalOpen(true);
      setUpdateError(null);
    }
  };

  // Handle closing the edit modal
  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setEditingMember(null);
    setSelectedNewOrganization(null);
    setSelectedNewDepartment(null);
    setEditingIsLeader(false);
    setAvailableDepartments([]);
    setUpdateError(null);
  };

  // Handle organization change in edit modal
  const handleEditOrgChange = async (organizationId: number) => {
    setSelectedNewOrganization(organizationId);
    setSelectedNewDepartment(null);

    try {
      const token = await getToken();
      if (!token) {
        setUpdateError('Authentication required');
        return;
      }

      const response = await fetch(`/api/v1/department?organizationId=${organizationId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch departments for the selected organization');
      }

      const data = await response.json();
      setAvailableDepartments(data.departments || []);
    } catch (err: any) {
      setUpdateError(err.message || 'Error fetching departments');
      console.error(err);
    }
  };

  // Handle saving member changes
  const handleSaveMember = async () => {
    if (!editingMember || !selectedNewDepartment) return;

    setIsUpdating(true);
    setUpdateError(null);

    try {
      const token = await getToken();
      if (!token) {
        setUpdateError('Authentication required');
        setIsUpdating(false);
        return;
      }

      // Check if department or isLeader has changed
      const departmentChanged = selectedNewDepartment !== editingMember.department.id;
      const isLeaderChanged = editingIsLeader !== editingMember.isLeader;
      
      if (departmentChanged || isLeaderChanged) {
        const updateData: any = {
          id: editingMember.id,
        };
        
        if (departmentChanged) {
          updateData.departmentId = selectedNewDepartment;
        }
        
        if (isLeaderChanged) {
          updateData.isLeader = editingIsLeader;
        }
        
        const response = await fetch('/api/v1/member', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(updateData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update member');
        }

        const data = await response.json();

        // Find the new organization name for the selected department
        const newDepartment = availableDepartments.find(dept => dept.id === selectedNewDepartment);
        const newOrganization = organizations.find(org => org.id === selectedNewOrganization);

        // Show success message
        let successMessage = 'Member updated successfully';
        if (departmentChanged && isLeaderChanged) {
          successMessage = `Member moved to ${newDepartment?.name || 'new department'} and leader status updated`;
        } else if (departmentChanged) {
          successMessage = `Member moved to ${newDepartment?.name || 'new department'} in ${newOrganization?.name || 'organization'}`;
        } else if (isLeaderChanged) {
          successMessage = `Member leader status updated to ${editingIsLeader ? 'Leader' : 'Member'}`;
        }
        
        toast.success(successMessage);

        // Re-fetch members data to ensure we have the latest data
        await fetchMembersData();
      }

      // Close the modal
      handleCloseEditModal();
    } catch (err: any) {
      setUpdateError(err.message || 'Error updating member');
      console.error(err);
    } finally {
      setIsUpdating(false);
    }
  };

  // Function to fetch members data that can be called from anywhere
  const fetchMembersData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      if (!token) {
        setError('Authentication required');
        setIsLoading(false);
        return;
      }

      let url = '/api/v1/member';
      const params = new URLSearchParams();

      if (isDepartmentLeader) {
        // For department leaders, only allow fetching from departments they lead
        if (selectedDept) {
          // Verify the selected department is one they lead
          const canAccessDept = leaderDepartments.some(dept => dept.id === selectedDept.id);
          if (!canAccessDept) {
            setError('You can only view members from departments you lead');
            setIsLoading(false);
            return;
          }
          params.append('departmentId', selectedDept.id.toString());
        } else {
          // No department selected, don't fetch anything yet
          setMembers([]);
          setIsLoading(false);
          return;
        }
      } else if (hasAdminPrivileges) {
        // For admin/owner users, use existing logic
        if (selectedDept) {
          params.append('departmentId', selectedDept.id.toString());
        } else if (selectedOrg) {
          params.append('organizationId', selectedOrg.id.toString());
        } else {
          // No filters selected, don't fetch anything yet
          setMembers([]);
          setIsLoading(false);
          return;
        }
      } else {
        // User doesn't have permission to view members
        setError('You do not have permission to view members');
        setIsLoading(false);
        return;
      }

      // Add name search parameter if provided
      if (searchName.trim()) {
        params.append('name', searchName.trim());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch members');
      }

      const data = await response.json();
      setMembers(data.members || []);
    } catch (err) {
      setError('Error fetching members');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  // Open delete confirmation modal
  const openDeleteModal = (memberId: number) => {
    // Find the member to delete
    const member = members.find(m => m.id === memberId);
    if (member) {
      setMemberToDelete(member);
      setIsDeleteModalOpen(true);
    }
  };

  // Open add member modal
  const handleOpenAddModal = () => {
    setIsAddModalOpen(true);
  };

  // Close add member modal
  const handleCloseAddModal = () => {
    setIsAddModalOpen(false);
  };

  // Handle chat with member
  const handleChatWithMember = (member: Member) => {
    if (!userData) {
      toast.error('Authentication required');
      return;
    }

    // Don't allow chatting with yourself
    if (userData.id === member.user.id) {
      toast.error('You cannot chat with yourself');
      return;
    }

    // Don't allow chatting with deleted users
    if (member.user.deletedAt) {
      toast.error('Cannot chat with deactivated members');
      return;
    }

    const targetUserName = `${member.user.firstName} ${member.user.lastName}`;
    navigateToPrivateChat(router, userData.id, member.user.id, targetUserName);
    toast.success(`Opening chat with ${targetUserName}...`);
  };

  // Close delete confirmation modal
  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setMemberToDelete(null);
  };

  // Handle delete member (soft delete)
  const handleDeleteMember = async () => {
    if (!memberToDelete) return;

    setIsDeleting(true);

    try {
      const token = await getToken();
      if (!token) {
        setError('Authentication required');
        setIsDeleting(false);
        return;
      }

      // Use the new soft delete endpoint
      const response = await fetch(`/api/v1/member/delete?id=${memberToDelete.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete member');
      }

      // Show success message
      toast.success('Member removed successfully');

      // Close the modal
      closeDeleteModal();

      // Re-fetch members data to ensure we have the latest data
      fetchMembersData();
    } catch (err: any) {
      setError(err.message || 'Error deleting member');
      toast.error(err.message || 'Error deleting member');
      console.error(err);
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle rollback member (restore deleted member)
  const openRestoreModal = (memberId: number) => {
    const member = members.find(m => m.id === memberId);
    if (member) {
      setMemberToRestore(member);
      setIsRestoreModalOpen(true);
    }
  };

  const handleRestoreSuccess = () => {
    // Refresh the members data after successful restoration
    fetchMembersData();
  };

  // This function is kept for backward compatibility but now just opens the modal
  const handleRollbackMember = async (memberId: number) => {
    openRestoreModal(memberId);
    try {
      const token = await getToken();
      if (!token) {
        setError('Authentication required');
        return;
      }

      // Find the user ID associated with this member
      const member = members.find(m => m.id === memberId);
      if (!member || !member.user) {
        setError('Member not found');
        return;
      }
      const userId = member.user.id;

      // Call the API to restore the user
      const response = await fetch(`/api/v1/user/restore?id=${userId}`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to restore member');
      }

      // Update the member in the state to remove the deletedAt flag
      setMembers(
        members.map(member =>
          member.user.id === userId
            ? { ...member, user: { ...member.user, deletedAt: undefined } }
            : member
        )
      );

      // Show success message
      toast.success('Member restored successfully');
    } catch (err: any) {
      setError(err.message || 'Error restoring member');
      toast.error(err.message || 'Error restoring member');
      console.error(err);
    }
  };

  return (
    <SettingsLayout>
      <MembersContainer suppressHydrationWarning>
        <Header>
          <Title>Members</Title>
          {(hasAdminPrivileges || isDepartmentLeader) && (
            <AddButton onClick={handleOpenAddModal}>
              <Plus size={16} />
              Add Member
            </AddButton>
          )}
        </Header>

        <FiltersContainer>
          <SearchInputContainer>
            <SearchIcon>
              <Search size={16} />
            </SearchIcon>
            <SearchInput
              type="text"
              placeholder="Search members by name or email..."
              value={searchName}
              onChange={(e) => handleSearchChange(e.target.value)}
            />
          </SearchInputContainer>

          {/* Show different filters based on user role */}
          {hasAdminPrivileges ? (
            // Full filter interface for owners and admins
            <>
              <FilterSelect>
                <FilterButton onClick={() => setOrgDropdownOpen(!orgDropdownOpen)}>
                  <FilterLabel>
                    <Filter size={14} />
                    {selectedOrg ? selectedOrg.name : 'Select Organization'}
                  </FilterLabel>
                  <ChevronDown size={14} />
                </FilterButton>
                <FilterDropdown $isOpen={orgDropdownOpen}>
                  {organizations.length > 0 ? (
                    organizations.map(org => (
                      <FilterOption
                        key={org.id}
                        onClick={() => handleOrgSelect(org)}
                        $isSelected={selectedOrg?.id === org.id}
                      >
                        {org.name}
                      </FilterOption>
                    ))
                  ) : (
                    <FilterOption>No organizations found</FilterOption>
                  )}
                </FilterDropdown>
              </FilterSelect>

              <FilterSelect>
                <FilterButton
                  onClick={() => setDeptDropdownOpen(!deptDropdownOpen)}
                  disabled={!selectedOrg}
                >
                  <FilterLabel>
                    <Filter size={14} />
                    {selectedDept ? selectedDept.name : 'Select Department'}
                  </FilterLabel>
                  <ChevronDown size={14} />
                </FilterButton>
                <FilterDropdown $isOpen={deptDropdownOpen}>
                  {departments.length > 0 ? (
                    departments.map(dept => (
                      <FilterOption
                        key={dept.id}
                        onClick={() => handleDeptSelect(dept)}
                        $isSelected={selectedDept?.id === dept.id}
                      >
                        {dept.name}
                      </FilterOption>
                    ))
                  ) : (
                    <FilterOption>
                      {selectedOrg ? 'No departments found' : 'Select an organization first'}
                    </FilterOption>
                  )}
                </FilterDropdown>
              </FilterSelect>
            </>
          ) : isDepartmentLeader ? (
            // Simplified filter interface for department leaders
            <FilterSelect>
              <FilterButton onClick={() => setDeptDropdownOpen(!deptDropdownOpen)}>
                <FilterLabel>
                  <Filter size={14} />
                  {selectedDept ? selectedDept.name : 'Select Department'}
                </FilterLabel>
                <ChevronDown size={14} />
              </FilterButton>
              <FilterDropdown $isOpen={deptDropdownOpen}>
                {leaderDepartments.length > 0 ? (
                  leaderDepartments.map(dept => (
                    <FilterOption
                      key={dept.id}
                      onClick={() => handleDeptSelect({ id: dept.id, name: dept.name })}
                      $isSelected={selectedDept?.id === dept.id}
                    >
                      {dept.name}
                    </FilterOption>
                  ))
                ) : (
                  <FilterOption>No departments available</FilterOption>
                )}
              </FilterDropdown>
            </FilterSelect>
          ) : null}

          {(selectedOrg || selectedDept || searchName.trim()) && (
            <ActionButton onClick={clearFilters}>Clear Filters</ActionButton>
          )}
        </FiltersContainer>

        <GlobalStyle />
        {isLoading ? (
          <SkeletonContainer>
            <SkeletonTable>
              <SkeletonTableHeader>
                <SkeletonTableHeaderCell />
                <SkeletonTableHeaderCell />
                <SkeletonTableHeaderCell />
                <SkeletonTableHeaderCell />
                <SkeletonTableHeaderCell />
              </SkeletonTableHeader>

              {[...Array(6)].map((_, index) => (
                <SkeletonTableRow key={index}>
                  <SkeletonUserCell>
                    <SkeletonAvatar />
                    <SkeletonUserInfo>
                      <SkeletonUserName />
                      <SkeletonUserEmail />
                    </SkeletonUserInfo>
                  </SkeletonUserCell>
                  <SkeletonBadge />
                  <SkeletonText className="department" />
                  <SkeletonText className="organization" />
                  <SkeletonActions>
                    <SkeletonActionButton />
                    <SkeletonActionButton />
                  </SkeletonActions>
                </SkeletonTableRow>
              ))}
            </SkeletonTable>
          </SkeletonContainer>
        ) : error ? (
          <div style={{ color: 'red', padding: '1rem' }}>{error}</div>
        ) : (
          <TableWrapper>
            <Table>
            <THead>
              <tr>
                <TH>User</TH>
                <TH>Role</TH>
                <TH>Leader</TH>
                <TH>Department</TH>
                <TH>Organization</TH>
                <TH>Actions</TH>
              </tr>
            </THead>
            <tbody>
              {filteredMembers.length > 0 ? (
                filteredMembers.map(member => (
                  <TR key={member.id} style={{ opacity: member.user.deletedAt ? '0.4' : '1' }}>
                    <TD>
                      <UserCell>
                        <Avatar $imageUrl={member.user.imageUrl}>
                          {member.user.firstName.charAt(0)}
                        </Avatar>
                        <UserInfo>
                          <UserName>{`${member.user.firstName} ${member.user.lastName}`}</UserName>
                          <UserEmail>{member.user.email}</UserEmail>
                        </UserInfo>
                      </UserCell>
                    </TD>
                    <TD>
                      <Badge>{member.user.userRoleId === 1 ? 'Admin' : 'Member'}</Badge>
                    </TD>
                    <TD>
                      {member.isLeader ? (
                        <Badge style={{ backgroundColor: '#fef3c7', color: '#d97706' }}>Leader</Badge>
                      ) : (
                        <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Member</span>
                      )}
                    </TD>
                    <TD>{member.department?.name || ''}</TD>
                    <TD>{member.department?.organization?.name || ''}</TD>
                    <TD>
                      <ActionButtonContainer>
                        {/* Chat button - Available to all users for non-deleted members */}
                        {!member.user.deletedAt && userData && userData.id !== member.user.id && (
                          <ActionButton
                            className="chat"
                            onClick={() => handleChatWithMember(member)}
                            title="Chat with Member"
                            style={{ color: '#3b82f6' }}
                          >
                            <MessageCircle size={16} />
                          </ActionButton>
                        )}

                        {/* Admin/Owner and Department Leader buttons */}
                        {(hasAdminPrivileges ||
                          (isDepartmentLeader && leaderDepartments.some(dept => dept.id === member.department.id))) && (
                          <>
                            {member?.user?.deletedAt ? (
                              <ActionButton
                                className="edit"
                                onClick={() => openRestoreModal(member.id)}
                                title="Restore Member"
                              >
                                <RotateCcw size={16} />
                              </ActionButton>
                            ) : (
                              <>
                                <ActionButton
                                  className="edit"
                                  onClick={() => handleEditMember(member.id)}
                                  title="Edit Member"
                                >
                                  <Edit size={16} />
                                </ActionButton>
                                <ActionButton
                                  className="delete"
                                  onClick={() => openDeleteModal(member.id)}
                                  title="Remove Member"
                                >
                                  <Trash size={16} />
                                </ActionButton>
                              </>
                            )}
                          </>
                        )}
                      </ActionButtonContainer>
                    </TD>
                  </TR>
                ))
              ) : (
                <TR>
                  <TD colSpan={5}>
                    <NoResults>
                      {selectedDept
                        ? 'No members found with the current filters'
                        : isDepartmentLeader
                        ? 'Please select a department to view members'
                        : selectedOrg
                        ? 'No members found with the current filters'
                        : 'Please select an organization or department to view members'}
                    </NoResults>
                  </TD>
                </TR>
              )}
            </tbody>
            </Table>
          </TableWrapper>
        )}
        {/* Edit Member Modal */}
        {isEditModalOpen && editingMember && (
          <ModalOverlay>
            <ModalContent>
              <ModalHeader>
                <ModalTitle>Edit Member</ModalTitle>
                <ModalCloseButton onClick={handleCloseEditModal}>
                  <X size={18} />
                </ModalCloseButton>
              </ModalHeader>
              <ModalBody>
                <FormGroup>
                  <FormLabel>Member</FormLabel>
                  <div>
                    {editingMember.user.firstName} {editingMember.user.lastName} (
                    {editingMember.user.email})
                  </div>
                </FormGroup>

                <FormGroup>
                  <FormLabel>Current Department</FormLabel>
                  <div>
                    {editingMember.department.name} ({editingMember.department.organization.name})
                  </div>
                </FormGroup>

                <FormGroup>
                  <FormLabel>Organization</FormLabel>
                  <div style={{ opacity: 0.6 }}>
                    <FormSelect
                      value={selectedNewOrganization || ''}
                      onChange={e => handleEditOrgChange(Number(e.target.value))}
                      disabled={true} /* Disabled organization selection */
                    >
                      <option value="">Select Organization</option>
                      {organizations.map(org => (
                        <option key={org.id} value={org.id}>
                          {org.name}
                        </option>
                      ))}
                    </FormSelect>
                  </div>
                </FormGroup>

                <FormGroup>
                  <FormLabel>New Department</FormLabel>
                  <FormSelect
                    value={selectedNewDepartment || ''}
                    onChange={e => setSelectedNewDepartment(Number(e.target.value))}
                    disabled={!selectedNewOrganization}
                  >
                    <option value="">Select Department</option>
                    {availableDepartments.map(dept => (
                      <option key={dept.id} value={dept.id}>
                        {dept.name}
                      </option>
                    ))}
                  </FormSelect>
                </FormGroup>

                <FormGroup>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <input
                      type="checkbox"
                      id="editIsLeader"
                      checked={editingIsLeader}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setEditingIsLeader(e.target.checked)
                      }
                    />
                    <FormLabel htmlFor="editIsLeader" style={{ margin: 0, cursor: 'pointer' }}>
                      Department Leader
                    </FormLabel>
                  </div>
                </FormGroup>

                {updateError && (
                  <div style={{ color: '#ef4444', marginTop: '0.5rem', fontSize: '0.875rem' }}>
                    {updateError}
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <CancelButton onClick={handleCloseEditModal} disabled={isUpdating}>
                  Cancel
                </CancelButton>
                <SaveButton
                  onClick={handleSaveMember}
                  disabled={
                    isUpdating || 
                    !selectedNewDepartment || 
                    (!editingMember || (
                      selectedNewDepartment === editingMember.department.id && 
                      editingIsLeader === editingMember.isLeader
                    ))
                  }
                >
                  {isUpdating ? <LoadingSpinner /> : null}
                  Save Changes
                </SaveButton>
              </ModalFooter>
            </ModalContent>
          </ModalOverlay>
        )}

        {/* Add Member Modal */}
        <CreateMemberModal
          isOpen={isAddModalOpen}
          onClose={handleCloseAddModal}
          onMemberAdded={fetchMembersData}
          hasAdminPrivileges={hasAdminPrivileges}
          isDepartmentLeader={isDepartmentLeader}
          leaderDepartments={leaderDepartments}
        />

        {/* Delete Member Modal */}
        {isDeleteModalOpen && memberToDelete && (
          <ModalOverlay onClick={closeDeleteModal}>
            <ModalContent onClick={e => e.stopPropagation()}>
              <ModalHeader>
                <DeleteModalTitle>
                  <AlertTriangle size={20} />
                  Remove Member
                </DeleteModalTitle>
                <ModalCloseButton onClick={closeDeleteModal}>
                  <X size={18} />
                </ModalCloseButton>
              </ModalHeader>
              <ModalBody>
                <WarningText>
                  Are you sure you want to remove this member? This action cannot be undone.
                </WarningText>

                <MemberPreview>
                  <UserCell>
                    <Avatar $imageUrl={memberToDelete.user.imageUrl}>
                      {memberToDelete.user.firstName.charAt(0)}
                    </Avatar>
                    <UserInfo>
                      <UserName>{`${memberToDelete.user.firstName} ${memberToDelete.user.lastName}`}</UserName>
                      <UserEmail>{memberToDelete.user.email}</UserEmail>
                    </UserInfo>
                  </UserCell>
                  <div style={{ marginTop: '0.5rem', fontSize: '0.875rem', color: '#666' }}>
                    Department: <strong>{memberToDelete.department.name}</strong>
                  </div>
                </MemberPreview>
              </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={closeDeleteModal}>
                  Cancel
                </CancelButton>
                <DeleteButton onClick={handleDeleteMember} disabled={isDeleting}>
                  {isDeleting ? 'Deleting...' : 'Delete Member'}
                </DeleteButton>
              </ModalFooter>
            </ModalContent>
          </ModalOverlay>
        )}

        {/* Restore Member Modal */}
        {isRestoreModalOpen && memberToRestore && (
          <RestoreMemberModal
            isOpen={isRestoreModalOpen}
            onClose={() => setIsRestoreModalOpen(false)}
            onSuccess={handleRestoreSuccess}
            member={memberToRestore}
          />
        )}
      </MembersContainer>
    </SettingsLayout>
  );
}
